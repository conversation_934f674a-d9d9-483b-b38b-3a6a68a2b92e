# Novu Template Deployment (Import/Export) Guide

## Overview

This guide explains how to use the CareMate API's Novu template deployment system to manage notification workflows through REST API endpoints. The system allows you to export, import, validate, and sync notification templates between different environments.

## Prerequisites

1. **Novu Configuration**: Ensure Novu is properly configured in your environment
2. **API Access**: Valid authentication token with `manage_notifications` permission
3. **Novu API Key**: Valid Novu API key configured in environment variables

## Environment Configuration

Add the following environment variables to your `.env` file:

```env
# Novu Configuration
NOVU_ENABLED=true
NOVU_API_KEY=your_novu_api_key_here
NOVU_APP_ID=your_novu_app_id_here
NOVU_DIGEST_WORKFLOW_ID=your_digest_workflow_id_here
```

## API Endpoints

### Base URL
All Novu template endpoints are available under:
```
/api/novu-templates
```

### 1. Workflow Management

#### Get All Workflows
```http
GET /api/novu-templates/workflows
```

**Query Parameters:**
- `page` (optional): Page number for pagination
- `limit` (optional): Number of workflows per page (max 100)

**Response:**
```json
{
  "success": true,
  "message": "Workflows retrieved successfully",
  "data": {
    "data": [...],
    "totalCount": 25,
    "page": 1
  }
}
```

#### Get Specific Workflow
```http
GET /api/novu-templates/workflows/{workflowId}
```

#### Create New Workflow
```http
POST /api/novu-templates/workflows
```

**Request Body:**
```json
{
  "name": "Welcome Email Workflow",
  "identifier": "welcome-email",
  "description": "Send welcome email to new users",
  "steps": [
    {
      "template": {
        "type": "email",
        "subject": "Welcome to CareMate!",
        "content": "Welcome {{firstName}}!"
      },
      "active": true
    }
  ]
}
```

#### Update Workflow
```http
PUT /api/novu-templates/workflows/{workflowId}
```

#### Delete Workflow
```http
DELETE /api/novu-templates/workflows/{workflowId}
```

#### Update Workflow Status
```http
PUT /api/novu-templates/workflows/{workflowId}/status
```

**Request Body:**
```json
{
  "active": true
}
```

### 2. Template Import/Export

#### Export Workflows
```http
POST /api/novu-templates/export
```

**Request Body (Optional):**
```json
{
  "workflowIds": ["workflow-1", "workflow-2"]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Workflows exported successfully",
  "data": {
    "exportedAt": "2024-01-15T10:30:00Z",
    "version": "1.0",
    "workflowsCount": 5,
    "workflows": [...]
  }
}
```

#### Import Workflows
```http
POST /api/novu-templates/import?overwriteExisting=false&skipExisting=true&updateIdentifiers=true
```

**Query Parameters:**
- `overwriteExisting`: Whether to overwrite existing workflows (default: false)
- `skipExisting`: Whether to skip existing workflows (default: true)
- `updateIdentifiers`: Whether to update workflow identifiers (default: true)

**Request Body:**
```json
{
  "exportedAt": "2024-01-15T10:30:00Z",
  "version": "1.0",
  "workflowsCount": 5,
  "workflows": [...]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Workflows imported successfully",
  "data": {
    "imported": [
      {
        "identifier": "welcome-email",
        "action": "created",
        "id": "new-workflow-id"
      }
    ],
    "skipped": [],
    "errors": [],
    "total": 5
  }
}
```

#### Validate Workflows
```http
POST /api/novu-templates/validate
```

**Request Body:**
```json
{
  "workflows": [...]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Workflow validation completed",
  "data": {
    "valid": true,
    "errors": [],
    "warnings": ["Workflow 1: identifier is missing, will be auto-generated"],
    "workflowsCount": 5
  }
}
```

#### Sync Workflows Between Environments
```http
POST /api/novu-templates/sync
```

**Request Body:**
```json
{
  "sourceWorkflowIds": ["workflow-1", "workflow-2"],
  "targetEnvironment": "production"
}
```

## Usage Examples

### 1. Complete Export/Import Workflow

#### Step 1: Export from Source Environment
```bash
curl -X POST "https://api-dev.caremate.com/api/novu-templates/export" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{}' \
  -o workflows-export.json
```

#### Step 2: Validate Before Import
```bash
curl -X POST "https://api-prod.caremate.com/api/novu-templates/validate" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d @workflows-export.json
```

#### Step 3: Import to Target Environment
```bash
curl -X POST "https://api-prod.caremate.com/api/novu-templates/import?overwriteExisting=false" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d @workflows-export.json
```

### 2. Selective Export/Import

#### Export Specific Workflows
```bash
curl -X POST "https://api-dev.caremate.com/api/novu-templates/export" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "workflowIds": ["welcome-email", "password-reset", "appointment-reminder"]
  }'
```

### 3. Environment Sync

#### Prepare Workflows for Sync
```bash
curl -X POST "https://api-dev.caremate.com/api/novu-templates/sync" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "sourceWorkflowIds": ["welcome-email"],
    "targetEnvironment": "production"
  }'
```

## Best Practices

### 1. Pre-Import Validation
Always validate workflows before importing:
```javascript
// Validate first
const validation = await fetch('/api/novu-templates/validate', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(importData)
});

if (validation.data.valid) {
  // Proceed with import
  const importResult = await fetch('/api/novu-templates/import', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(importData)
  });
}
```

### 2. Backup Before Import
```javascript
// Export current workflows as backup
const backup = await fetch('/api/novu-templates/export', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({})
});

// Save backup before importing new workflows
localStorage.setItem('novu-backup', JSON.stringify(backup.data));
```

### 3. Incremental Updates
Use selective export/import for specific workflows:
```javascript
// Export only modified workflows
const modifiedWorkflows = ['workflow-1', 'workflow-2'];
const exportData = await fetch('/api/novu-templates/export', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ workflowIds: modifiedWorkflows })
});
```

## Error Handling

### Common Error Scenarios

1. **Invalid API Key**: Ensure `NOVU_ENABLED=true` and valid `NOVU_API_KEY`
2. **Permission Denied**: User must have `manage_notifications` permission
3. **Workflow Not Found**: Check workflow ID exists in Novu
4. **Import Conflicts**: Use appropriate query parameters for conflict resolution

### Error Response Format
```json
{
  "success": false,
  "message": "Error message",
  "error": "Detailed error information"
}
```

## Security Considerations

1. **API Key Protection**: Store Novu API keys securely
2. **Permission Control**: Restrict `manage_notifications` permission to authorized users
3. **Environment Isolation**: Use separate Novu environments for dev/staging/production
4. **Audit Logging**: All template operations are logged for audit purposes

## Troubleshooting

### Common Issues

1. **Novu Service Not Enabled**
   - Check `NOVU_ENABLED=true` in environment
   - Verify `NOVU_API_KEY` is set

2. **Import Failures**
   - Validate workflows before import
   - Check for identifier conflicts
   - Review error messages in import response

3. **Permission Errors**
   - Ensure user has `manage_notifications` permission
   - Check authentication token validity

### Debug Mode
Enable debug logging by setting `LOG_LEVEL=debug` in your environment.

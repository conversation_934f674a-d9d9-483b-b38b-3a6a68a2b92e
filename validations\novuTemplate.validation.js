const Joi = require("joi");

const getWorkflows = {
  query: Joi.object().keys({
    page: Joi.number().integer().min(1).optional(),
    limit: Joi.number().integer().min(1).max(100).optional()
  })
};

const getWorkflow = {
  params: Joi.object().keys({
    workflowId: Joi.string().required()
  })
};

const createWorkflow = {
  body: Joi.object().keys({
    name: Joi.string().required().min(1).max(255),
    identifier: Joi.string().optional().pattern(/^[a-zA-Z0-9_-]+$/),
    description: Joi.string().optional().max(1000),
    tags: Joi.array().items(Joi.string()).optional(),
    active: Joi.boolean().optional().default(true),
    draft: Joi.boolean().optional().default(false),
    critical: Joi.boolean().optional().default(false),
    steps: Joi.array().items(
      Joi.object().keys({
        template: Joi.object().keys({
          type: Joi.string().valid('email', 'sms', 'in_app', 'push', 'chat', 'digest').required(),
          subject: Joi.string().when('type', { is: 'email', then: Joi.required(), otherwise: Joi.optional() }),
          content: Joi.string().required(),
          contentType: Joi.string().valid('editor', 'customHtml').optional(),
          variables: Joi.array().items(Joi.object()).optional(),
          cta: Joi.object().optional(),
          feedId: Joi.string().when('type', { is: 'in_app', then: Joi.optional(), otherwise: Joi.forbidden() }),
          actor: Joi.object().optional()
        }).required(),
        filters: Joi.array().optional(),
        active: Joi.boolean().optional().default(true),
        shouldStopOnFail: Joi.boolean().optional().default(false),
        metadata: Joi.object().optional()
      })
    ).required().min(1),
    notificationGroupId: Joi.string().optional(),
    data: Joi.object().optional()
  })
};

const updateWorkflow = {
  params: Joi.object().keys({
    workflowId: Joi.string().required()
  }),
  body: Joi.object().keys({
    name: Joi.string().optional().min(1).max(255),
    identifier: Joi.string().optional().pattern(/^[a-zA-Z0-9_-]+$/),
    description: Joi.string().optional().max(1000),
    tags: Joi.array().items(Joi.string()).optional(),
    active: Joi.boolean().optional(),
    draft: Joi.boolean().optional(),
    critical: Joi.boolean().optional(),
    steps: Joi.array().items(
      Joi.object().keys({
        template: Joi.object().required(),
        filters: Joi.array().optional(),
        active: Joi.boolean().optional(),
        shouldStopOnFail: Joi.boolean().optional(),
        metadata: Joi.object().optional()
      })
    ).optional(),
    notificationGroupId: Joi.string().optional(),
    data: Joi.object().optional()
  })
};

const deleteWorkflow = {
  params: Joi.object().keys({
    workflowId: Joi.string().required()
  })
};

const updateWorkflowStatus = {
  params: Joi.object().keys({
    workflowId: Joi.string().required()
  }),
  body: Joi.object().keys({
    active: Joi.boolean().required()
  })
};

const exportWorkflows = {
  body: Joi.object().keys({
    workflowIds: Joi.array().items(Joi.string()).optional()
  })
};

const importWorkflows = {
  query: Joi.object().keys({
    overwriteExisting: Joi.string().valid('true', 'false').optional().default('false'),
    skipExisting: Joi.string().valid('true', 'false').optional().default('true'),
    updateIdentifiers: Joi.string().valid('true', 'false').optional().default('true')
  }),
  body: Joi.object().keys({
    exportedAt: Joi.string().isoDate().optional(),
    version: Joi.string().optional(),
    workflowsCount: Joi.number().integer().min(0).optional(),
    workflows: Joi.array().items(
      Joi.object().keys({
        name: Joi.string().required(),
        identifier: Joi.string().optional(),
        description: Joi.string().optional(),
        tags: Joi.array().items(Joi.string()).optional(),
        active: Joi.boolean().optional(),
        draft: Joi.boolean().optional(),
        critical: Joi.boolean().optional(),
        steps: Joi.array().items(Joi.object()).required(),
        notificationGroupId: Joi.string().optional(),
        data: Joi.object().optional()
      })
    ).required().min(1)
  })
};

const syncWorkflows = {
  body: Joi.object().keys({
    sourceWorkflowIds: Joi.array().items(Joi.string()).optional(),
    targetEnvironment: Joi.string().required()
  })
};

const validateWorkflows = {
  body: Joi.object().keys({
    exportedAt: Joi.string().isoDate().optional(),
    version: Joi.string().optional(),
    workflowsCount: Joi.number().integer().min(0).optional(),
    workflows: Joi.array().items(Joi.object()).required()
  })
};

module.exports = {
  getWorkflows,
  getWorkflow,
  createWorkflow,
  updateWorkflow,
  deleteWorkflow,
  updateWorkflowStatus,
  exportWorkflows,
  importWorkflows,
  syncWorkflows,
  validateWorkflows
};

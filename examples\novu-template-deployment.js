/**
 * Novu Template Deployment Example
 * 
 * This script demonstrates how to use the CareMate API's Novu template
 * deployment endpoints for importing and exporting notification workflows.
 */

const fetch = require('node-fetch'); // You may need to install: npm install node-fetch

class NovuTemplateManager {
  constructor(baseUrl, authToken) {
    this.baseUrl = baseUrl;
    this.authToken = authToken;
    this.headers = {
      'Authorization': `Bearer ${authToken}`,
      'Content-Type': 'application/json'
    };
  }

  async makeRequest(endpoint, method = 'GET', body = null) {
    const url = `${this.baseUrl}/api/novu-templates${endpoint}`;
    const options = {
      method,
      headers: this.headers
    };

    if (body) {
      options.body = JSON.stringify(body);
    }

    try {
      const response = await fetch(url, options);
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(`API Error: ${response.status} - ${data.message || 'Unknown error'}`);
      }
      
      return data;
    } catch (error) {
      console.error(`Request failed for ${endpoint}:`, error.message);
      throw error;
    }
  }

  // Get all workflows
  async getWorkflows(page = 1, limit = 10) {
    return this.makeRequest(`/workflows?page=${page}&limit=${limit}`);
  }

  // Get specific workflow
  async getWorkflow(workflowId) {
    return this.makeRequest(`/workflows/${workflowId}`);
  }

  // Create new workflow
  async createWorkflow(workflowData) {
    return this.makeRequest('/workflows', 'POST', workflowData);
  }

  // Update workflow
  async updateWorkflow(workflowId, workflowData) {
    return this.makeRequest(`/workflows/${workflowId}`, 'PUT', workflowData);
  }

  // Delete workflow
  async deleteWorkflow(workflowId) {
    return this.makeRequest(`/workflows/${workflowId}`, 'DELETE');
  }

  // Update workflow status
  async updateWorkflowStatus(workflowId, active) {
    return this.makeRequest(`/workflows/${workflowId}/status`, 'PUT', { active });
  }

  // Export workflows
  async exportWorkflows(workflowIds = null) {
    const body = workflowIds ? { workflowIds } : {};
    return this.makeRequest('/export', 'POST', body);
  }

  // Import workflows
  async importWorkflows(importData, options = {}) {
    const queryParams = new URLSearchParams({
      overwriteExisting: options.overwriteExisting || 'false',
      skipExisting: options.skipExisting || 'true',
      updateIdentifiers: options.updateIdentifiers || 'true'
    });
    
    return this.makeRequest(`/import?${queryParams}`, 'POST', importData);
  }

  // Validate workflows
  async validateWorkflows(workflowData) {
    return this.makeRequest('/validate', 'POST', workflowData);
  }

  // Sync workflows
  async syncWorkflows(sourceWorkflowIds, targetEnvironment) {
    return this.makeRequest('/sync', 'POST', {
      sourceWorkflowIds,
      targetEnvironment
    });
  }
}

// Example usage functions
async function demonstrateExportImport() {
  const sourceEnv = new NovuTemplateManager('https://api-dev.caremate.com', 'DEV_TOKEN');
  const targetEnv = new NovuTemplateManager('https://api-prod.caremate.com', 'PROD_TOKEN');

  try {
    console.log('🚀 Starting Novu Template Deployment Demo...\n');

    // 1. List workflows in source environment
    console.log('📋 Getting workflows from source environment...');
    const sourceWorkflows = await sourceEnv.getWorkflows();
    console.log(`Found ${sourceWorkflows.data.data.length} workflows in source\n`);

    // 2. Export specific workflows
    console.log('📤 Exporting specific workflows...');
    const workflowIds = sourceWorkflows.data.data.slice(0, 2).map(w => w.identifier);
    const exportData = await sourceEnv.exportWorkflows(workflowIds);
    console.log(`Exported ${exportData.data.workflowsCount} workflows\n`);

    // 3. Validate before import
    console.log('✅ Validating workflows before import...');
    const validation = await targetEnv.validateWorkflows(exportData.data);
    console.log(`Validation result: ${validation.data.valid ? 'PASSED' : 'FAILED'}`);
    
    if (validation.data.errors.length > 0) {
      console.log('Validation errors:', validation.data.errors);
    }
    
    if (validation.data.warnings.length > 0) {
      console.log('Validation warnings:', validation.data.warnings);
    }
    console.log('');

    // 4. Import to target environment (only if validation passed)
    if (validation.data.valid) {
      console.log('📥 Importing workflows to target environment...');
      const importResult = await targetEnv.importWorkflows(exportData.data, {
        overwriteExisting: 'false',
        skipExisting: 'true',
        updateIdentifiers: 'true'
      });
      
      console.log(`Import completed:`);
      console.log(`- Imported: ${importResult.data.imported.length}`);
      console.log(`- Skipped: ${importResult.data.skipped.length}`);
      console.log(`- Errors: ${importResult.data.errors.length}`);
      
      if (importResult.data.errors.length > 0) {
        console.log('Import errors:', importResult.data.errors);
      }
    } else {
      console.log('❌ Skipping import due to validation failures');
    }

    console.log('\n✨ Demo completed successfully!');

  } catch (error) {
    console.error('❌ Demo failed:', error.message);
  }
}

async function demonstrateWorkflowManagement() {
  const manager = new NovuTemplateManager('https://api-dev.caremate.com', 'YOUR_TOKEN');

  try {
    console.log('🔧 Demonstrating Workflow Management...\n');

    // Create a new workflow
    const newWorkflow = {
      name: 'Test Notification Workflow',
      identifier: 'test-notification',
      description: 'A test workflow for demonstration',
      steps: [
        {
          template: {
            type: 'email',
            subject: 'Test Notification',
            content: 'Hello {{firstName}}, this is a test notification!'
          },
          active: true,
          shouldStopOnFail: false
        }
      ],
      active: true
    };

    console.log('📝 Creating new workflow...');
    const created = await manager.createWorkflow(newWorkflow);
    const workflowId = created.data._id;
    console.log(`Created workflow with ID: ${workflowId}\n`);

    // Get the created workflow
    console.log('📖 Retrieving created workflow...');
    const retrieved = await manager.getWorkflow(workflowId);
    console.log(`Retrieved workflow: ${retrieved.data.name}\n`);

    // Update workflow status
    console.log('⏸️ Deactivating workflow...');
    await manager.updateWorkflowStatus(workflowId, false);
    console.log('Workflow deactivated\n');

    // Reactivate workflow
    console.log('▶️ Reactivating workflow...');
    await manager.updateWorkflowStatus(workflowId, true);
    console.log('Workflow reactivated\n');

    // Clean up - delete the test workflow
    console.log('🗑️ Cleaning up - deleting test workflow...');
    await manager.deleteWorkflow(workflowId);
    console.log('Test workflow deleted\n');

    console.log('✨ Workflow management demo completed!');

  } catch (error) {
    console.error('❌ Workflow management demo failed:', error.message);
  }
}

async function demonstrateBackupRestore() {
  const manager = new NovuTemplateManager('https://api-dev.caremate.com', 'YOUR_TOKEN');

  try {
    console.log('💾 Demonstrating Backup and Restore...\n');

    // Create backup
    console.log('📤 Creating full backup of all workflows...');
    const backup = await manager.exportWorkflows();
    console.log(`Backup created with ${backup.data.workflowsCount} workflows\n`);

    // Save backup to file (in real scenario)
    const backupFilename = `novu-backup-${new Date().toISOString().split('T')[0]}.json`;
    console.log(`💾 Backup would be saved as: ${backupFilename}\n`);

    // Simulate restore validation
    console.log('✅ Validating backup for restore...');
    const validation = await manager.validateWorkflows(backup.data);
    console.log(`Backup validation: ${validation.data.valid ? 'VALID' : 'INVALID'}\n`);

    console.log('✨ Backup and restore demo completed!');

  } catch (error) {
    console.error('❌ Backup and restore demo failed:', error.message);
  }
}

// Main execution
async function main() {
  console.log('🎯 Novu Template Deployment Examples\n');
  console.log('Choose a demo to run:');
  console.log('1. Export/Import Demo');
  console.log('2. Workflow Management Demo');
  console.log('3. Backup/Restore Demo\n');

  // For this example, we'll run the export/import demo
  // In a real scenario, you'd get user input or use command line arguments
  
  const demoChoice = process.argv[2] || '1';
  
  switch (demoChoice) {
    case '1':
      await demonstrateExportImport();
      break;
    case '2':
      await demonstrateWorkflowManagement();
      break;
    case '3':
      await demonstrateBackupRestore();
      break;
    default:
      console.log('Invalid choice. Running export/import demo...');
      await demonstrateExportImport();
  }
}

// Run the demo if this file is executed directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { NovuTemplateManager };

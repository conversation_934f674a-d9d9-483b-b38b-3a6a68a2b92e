const express = require("express");
const auth = require("../middlewares/auth");
const validate = require("../middlewares/validate");
const { NovuTemplateController } = require("../controllers");
const { NovuTemplateValidation } = require("../validations");

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Novu Templates
 *   description: Novu workflow and template management
 */

/**
 * @swagger
 * /novu-templates/workflows:
 *   get:
 *     summary: Get all workflows from Novu
 *     description: Retrieve all notification workflows from Novu with optional pagination
 *     tags: [Novu Templates]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of workflows per page
 *     responses:
 *       200:
 *         description: Workflows retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         type: object
 *                     totalCount:
 *                       type: integer
 *                     page:
 *                       type: integer
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get(
  "/workflows",
  auth("manage_notifications"),
  validate(NovuTemplateValidation.getWorkflows),
  NovuTemplateController.getWorkflows
);

/**
 * @swagger
 * /api/novu-templates/workflows/{workflowId}:
 *   get:
 *     summary: Get a specific workflow
 *     description: Retrieve a specific notification workflow by ID
 *     tags: [Novu Templates]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: workflowId
 *         required: true
 *         schema:
 *           type: string
 *         description: Workflow identifier
 *     responses:
 *       200:
 *         description: Workflow retrieved successfully
 *       404:
 *         description: Workflow not found
 *       401:
 *         description: Unauthorized
 */
router.get(
  "/workflows/:workflowId",
  auth("manage_notifications"),
  // validate(NovuTemplateValidation.getWorkflow),
  NovuTemplateController.getWorkflow
);

/**
 * @swagger
 * /novu-templates/workflows:
 *   post:
 *     summary: Create a new workflow
 *     description: Create a new notification workflow in Novu
 *     tags: [Novu Templates]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - steps
 *             properties:
 *               name:
 *                 type: string
 *                 description: Workflow name
 *               identifier:
 *                 type: string
 *                 description: Unique workflow identifier
 *               description:
 *                 type: string
 *                 description: Workflow description
 *               steps:
 *                 type: array
 *                 items:
 *                   type: object
 *                 description: Workflow steps configuration
 *     responses:
 *       201:
 *         description: Workflow created successfully
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 */
router.post(
  "/workflows",
  // auth("manage_notifications"),
  validate(NovuTemplateValidation.createWorkflow),
  NovuTemplateController.createWorkflow
);

/**
 * @swagger
 * /api/novu-templates/workflows/{workflowId}:
 *   put:
 *     summary: Update a workflow
 *     description: Update an existing notification workflow
 *     tags: [Novu Templates]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: workflowId
 *         required: true
 *         schema:
 *           type: string
 *         description: Workflow identifier
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               steps:
 *                 type: array
 *                 items:
 *                   type: object
 *     responses:
 *       200:
 *         description: Workflow updated successfully
 *       404:
 *         description: Workflow not found
 *       401:
 *         description: Unauthorized
 */
router.put(
  "/workflows/:workflowId",
  auth("manage_notifications"),
  // validate(NovuTemplateValidation.updateWorkflow),
  NovuTemplateController.updateWorkflow
);

/**
 * @swagger
 * /api/novu-templates/workflows/{workflowId}:
 *   delete:
 *     summary: Delete a workflow
 *     description: Delete a notification workflow from Novu
 *     tags: [Novu Templates]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: workflowId
 *         required: true
 *         schema:
 *           type: string
 *         description: Workflow identifier
 *     responses:
 *       200:
 *         description: Workflow deleted successfully
 *       404:
 *         description: Workflow not found
 *       401:
 *         description: Unauthorized
 */
router.delete(
  "/workflows/:workflowId",
  auth("manage_notifications"),
  // validate(NovuTemplateValidation.deleteWorkflow),
  NovuTemplateController.deleteWorkflow
);

/**
 * @swagger
 * /api/novu-templates/workflows/{workflowId}/status:
 *   put:
 *     summary: Update workflow status
 *     description: Activate or deactivate a notification workflow
 *     tags: [Novu Templates]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: workflowId
 *         required: true
 *         schema:
 *           type: string
 *         description: Workflow identifier
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - active
 *             properties:
 *               active:
 *                 type: boolean
 *                 description: Whether the workflow should be active
 *     responses:
 *       200:
 *         description: Workflow status updated successfully
 *       404:
 *         description: Workflow not found
 *       401:
 *         description: Unauthorized
 */
router.put(
  "/workflows/:workflowId/status",
  auth("manage_notifications"),
  // validate(NovuTemplateValidation.updateWorkflowStatus),
  NovuTemplateController.updateWorkflowStatus
);

/**
 * @swagger
 * /api/novu-templates/export:
 *   post:
 *     summary: Export workflows
 *     description: Export notification workflows as JSON for backup or migration
 *     tags: [Novu Templates]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               workflowIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Optional array of specific workflow IDs to export
 *     responses:
 *       200:
 *         description: Workflows exported successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 exportedAt:
 *                   type: string
 *                   format: date-time
 *                 version:
 *                   type: string
 *                 workflowsCount:
 *                   type: integer
 *                 workflows:
 *                   type: array
 *                   items:
 *                     type: object
 *       401:
 *         description: Unauthorized
 */
router.post(
  "/export",
  auth("manage_notifications"),
  // validate(NovuTemplateValidation.exportWorkflows),
  NovuTemplateController.exportWorkflows
);

/**
 * @swagger
 * /api/novu-templates/import:
 *   post:
 *     summary: Import workflows
 *     description: Import notification workflows from exported JSON data
 *     tags: [Novu Templates]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: overwriteExisting
 *         schema:
 *           type: string
 *           enum: ['true', 'false']
 *           default: 'false'
 *         description: Whether to overwrite existing workflows
 *       - in: query
 *         name: skipExisting
 *         schema:
 *           type: string
 *           enum: ['true', 'false']
 *           default: 'true'
 *         description: Whether to skip existing workflows
 *       - in: query
 *         name: updateIdentifiers
 *         schema:
 *           type: string
 *           enum: ['true', 'false']
 *           default: 'true'
 *         description: Whether to update workflow identifiers
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - workflows
 *             properties:
 *               workflows:
 *                 type: array
 *                 items:
 *                   type: object
 *                 description: Array of workflows to import
 *     responses:
 *       200:
 *         description: Workflows imported successfully
 *       400:
 *         description: Invalid import data
 *       401:
 *         description: Unauthorized
 */
router.post(
  "/import",
  auth("manage_notifications"),
  // validate(NovuTemplateValidation.importWorkflows),
  NovuTemplateController.importWorkflows
);

/**
 * @swagger
 * /api/novu-templates/validate:
 *   post:
 *     summary: Validate workflows before import
 *     description: Validate workflow configuration data before importing
 *     tags: [Novu Templates]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - workflows
 *             properties:
 *               workflows:
 *                 type: array
 *                 items:
 *                   type: object
 *                 description: Array of workflows to validate
 *     responses:
 *       200:
 *         description: Validation completed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 valid:
 *                   type: boolean
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: string
 *                 warnings:
 *                   type: array
 *                   items:
 *                     type: string
 *                 workflowsCount:
 *                   type: integer
 *       401:
 *         description: Unauthorized
 */
router.post(
  "/validate",
  auth("manage_notifications"),
  // validate(NovuTemplateValidation.validateWorkflows),
  NovuTemplateController.validateWorkflows
);

/**
 * @swagger
 * /api/novu-templates/sync:
 *   post:
 *     summary: Sync workflows between environments
 *     description: Export workflows from current environment for import to another environment
 *     tags: [Novu Templates]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - targetEnvironment
 *             properties:
 *               sourceWorkflowIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Optional array of specific workflow IDs to sync
 *               targetEnvironment:
 *                 type: string
 *                 description: Target environment identifier
 *     responses:
 *       200:
 *         description: Workflows prepared for sync
 *       401:
 *         description: Unauthorized
 */
router.post(
  "/sync",
  auth("manage_notifications"),
  // validate(NovuTemplateValidation.syncWorkflows),
  NovuTemplateController.syncWorkflows
);

module.exports = router;

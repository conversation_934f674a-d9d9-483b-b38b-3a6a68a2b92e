const httpStatus = require("http-status");
const { sendSuccess, sendError, catchAsync } = require("../helpers/api.helper");
const novuService = require("../services/novu.service");
const logger = require("../config/logger");

/**
 * @desc Get all workflows from Novu
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object containing workflows list
 */
exports.getWorkflows = catchAsync(async (req, res) => {
  const { page, limit } = req.query;
  
  const workflows = await novuService.getWorkflows({ page, limit });
  
  sendSuccess(res, "Workflows retrieved successfully", httpStatus.OK, workflows);
});

/**
 * @desc Get a specific workflow by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object containing workflow details
 */
exports.getWorkflow = catchAsync(async (req, res) => {
  const { workflowId } = req.params;
  
  const workflow = await novuService.getWorkflow(workflowId);
  
  sendSuccess(res, "Workflow retrieved successfully", httpStatus.OK, workflow);
});

/**
 * @desc Create a new workflow in Novu
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object containing created workflow
 */
exports.createWorkflow = catchAsync(async (req, res) => {
  const workflowData = req.body;
  
  const workflow = await novuService.createWorkflow(workflowData);
  
  sendSuccess(res, "Workflow created successfully", httpStatus.CREATED, workflow);
});

/**
 * @desc Update an existing workflow
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object containing updated workflow
 */
exports.updateWorkflow = catchAsync(async (req, res) => {
  const { workflowId } = req.params;
  const workflowData = req.body;
  
  const workflow = await novuService.updateWorkflow(workflowId, workflowData);
  
  sendSuccess(res, "Workflow updated successfully", httpStatus.OK, workflow);
});

/**
 * @desc Delete a workflow from Novu
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object containing deletion result
 */
exports.deleteWorkflow = catchAsync(async (req, res) => {
  const { workflowId } = req.params;
  
  const result = await novuService.deleteWorkflow(workflowId);
  
  sendSuccess(res, "Workflow deleted successfully", httpStatus.OK, result);
});

/**
 * @desc Update workflow status (active/inactive)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object containing updated workflow status
 */
exports.updateWorkflowStatus = catchAsync(async (req, res) => {
  const { workflowId } = req.params;
  const { active } = req.body;
  
  const result = await novuService.updateWorkflowStatus(workflowId, active);
  
  sendSuccess(res, "Workflow status updated successfully", httpStatus.OK, result);
});

/**
 * @desc Export workflows as JSON
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object containing exported workflows
 */
exports.exportWorkflows = catchAsync(async (req, res) => {
  const { workflowIds } = req.body; // Optional array of specific workflow IDs
  
  const exportData = await novuService.exportWorkflows(workflowIds);
  
  // Set headers for file download
  res.setHeader('Content-Type', 'application/json');
  res.setHeader('Content-Disposition', `attachment; filename="novu-workflows-export-${Date.now()}.json"`);
  
  sendSuccess(res, "Workflows exported successfully", httpStatus.OK, exportData);
});

/**
 * @desc Import workflows from JSON data
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object containing import results
 */
exports.importWorkflows = catchAsync(async (req, res) => {
  const importData = req.body;
  const { 
    overwriteExisting = false, 
    skipExisting = true,
    updateIdentifiers = true 
  } = req.query;
  
  const options = {
    overwriteExisting: overwriteExisting === 'true',
    skipExisting: skipExisting === 'true',
    updateIdentifiers: updateIdentifiers === 'true'
  };
  
  const results = await novuService.importWorkflows(importData, options);
  
  sendSuccess(res, "Workflows imported successfully", httpStatus.OK, results);
});

/**
 * @desc Sync workflows between environments
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object containing sync results
 */
exports.syncWorkflows = catchAsync(async (req, res) => {
  const { sourceWorkflowIds, targetEnvironment } = req.body;
  
  try {
    // Export from current environment
    const exportData = await novuService.exportWorkflows(sourceWorkflowIds);
    
    // This would require multiple Novu service instances for different environments
    // For now, we'll return the export data for manual import
    const syncResults = {
      exported: exportData.workflowsCount,
      exportData: exportData,
      message: "Workflows exported successfully. Use the import endpoint in the target environment to complete the sync."
    };
    
    sendSuccess(res, "Workflow sync prepared successfully", httpStatus.OK, syncResults);
  } catch (error) {
    logger.error('Error syncing workflows:', error.message);
    sendError(res, "Failed to sync workflows", httpStatus.INTERNAL_SERVER_ERROR, error.message);
  }
});

/**
 * @desc Validate workflow configuration before import
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object containing validation results
 */
exports.validateWorkflows = catchAsync(async (req, res) => {
  const importData = req.body;
  
  const validationResults = {
    valid: true,
    errors: [],
    warnings: [],
    workflowsCount: 0
  };
  
  try {
    if (!importData.workflows || !Array.isArray(importData.workflows)) {
      validationResults.valid = false;
      validationResults.errors.push("Invalid import data: workflows array is required");
    } else {
      validationResults.workflowsCount = importData.workflows.length;
      
      // Validate each workflow
      importData.workflows.forEach((workflow, index) => {
        if (!workflow.name) {
          validationResults.errors.push(`Workflow ${index + 1}: name is required`);
          validationResults.valid = false;
        }
        
        if (!workflow.identifier) {
          validationResults.warnings.push(`Workflow ${index + 1}: identifier is missing, will be auto-generated`);
        }
        
        if (!workflow.steps || !Array.isArray(workflow.steps)) {
          validationResults.errors.push(`Workflow ${index + 1}: steps array is required`);
          validationResults.valid = false;
        }
      });
    }
    
    sendSuccess(res, "Workflow validation completed", httpStatus.OK, validationResults);
  } catch (error) {
    logger.error('Error validating workflows:', error.message);
    sendError(res, "Failed to validate workflows", httpStatus.INTERNAL_SERVER_ERROR, error.message);
  }
});
